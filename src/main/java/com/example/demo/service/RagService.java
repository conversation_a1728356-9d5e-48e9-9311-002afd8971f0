package com.example.demo.service;

import dev.langchain4j.model.openai.OpenAiChatModel;
import dev.langchain4j.model.openai.OpenAiEmbeddingModel;
import dev.langchain4j.model.openai.OpenAiEmbeddingModelName;
import dev.langchain4j.rag.content.Content;
import dev.langchain4j.rag.content.retriever.EmbeddingStoreContentRetriever;
import dev.langchain4j.rag.query.Query;
import dev.langchain4j.store.embedding.chroma.ChromaEmbeddingStore;

import org.springframework.stereotype.Service;
import java.time.Duration;
import java.util.List;

@Service
public class RagService {
    
    private final String languageRules;
    private final String styleGuide;
    private final String jargonGuide;
    
    public RagService() {
        this.languageRules = "Follow below linguistic rules to generate response \n" + 
                // Language rules content omitted for brevity
                "**Notes**: \n" + 
                "- Focus on commonly encountered rules and differences, particularly for international audiences.\n" + 
                "- Avoid overly technical language or deep linguistic jargon—aim for accessibility and clarity.\n";
        
        this.styleGuide = "Structure generated response based below Australian Government Style Guide:\n" + 
                // Style guide content omitted for brevity
                "**Notes**: \n" + 
                "- Avoid unnecessary technical language.\n" + 
                "- Ensure consistent style and tone across all sections.\n";
        
        this.jargonGuide = "**Objective**: While generating response below guide for simplifying regulatory jargon into common, accessible language suitable for Australian audiences.\n" + 
                // Jargon guide content omitted for brevity
                "**Notes**:\n" + 
                "- Focus on creating a guide that helps government and regulatory bodies communicate more effectively with the general public.\n" + 
                "- The goal is not just simplification but also maintaining accuracy and trustworthiness in the information.\n";
    }
    
    public String generateResponse(String apiKey, String dataset, String question) {
        // Build ChromaDB embedding store
        ChromaEmbeddingStore embeddingStore = ChromaEmbeddingStore.builder()
                .baseUrl("http://chromadb:8000")
                .collectionName(dataset)
                .build();
        
        // Build embedding model
        OpenAiEmbeddingModel embeddingModel = OpenAiEmbeddingModel.builder()
                .apiKey(apiKey)
                .modelName(OpenAiEmbeddingModelName.TEXT_EMBEDDING_ADA_002)
                .build();
        
        // Create content retriever
        EmbeddingStoreContentRetriever contentRetriever = 
                new EmbeddingStoreContentRetriever(embeddingStore, embeddingModel, 10);
        
        // Retrieve relevant content
        List<Content> contentList = contentRetriever.retrieve(new Query(question));
        
        // Build context from retrieved content
        StringBuilder ragContext = new StringBuilder();
        for (Content content : contentList) {
            ragContext.append(content.textSegment().text());
            ragContext.append("\n\n");
        }
        
        // Build chat model
        OpenAiChatModel chatModel = OpenAiChatModel.builder()
                .modelName("gpt-4o")
                .apiKey(apiKey)
                .timeout(Duration.ofSeconds(300L))
                .build();
        
        // Generate response
        String prompt = languageRules + styleGuide + jargonGuide + 
                ragContext.toString() + "\n" + 
                "Answer below question based on above information. Also you must respond in HTML format" + 
                "\n" + question;
        
        return chatModel.generate(prompt);
    }
}